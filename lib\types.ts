// User roles in the system
export type UserRole = 'STUDENT' | 'INSTRUCTOR' | 'PROCTOR' | 'ADMIN'

// User interface
export interface User {
  id: string
  email: string
  name: string
  role: UserRole
  studentId?: string // For students
  employeeId?: string // For staff (instructors, proctors, admins)
  department?: string
  profileImage?: string
  isActive: boolean
  emailVerified: boolean
  createdAt: Date
  updatedAt: Date
}

// Authentication interfaces
export interface SignupData {
  email: string
  password: string
  name: string
  role: UserRole
  studentId?: string
  employeeId?: string
  department?: string
}

export interface LoginData {
  email: string
  password: string
}

// Exam related types
export interface Exam {
  id: string
  title: string
  description: string
  instructorId: string
  duration: number // in minutes
  totalMarks: number
  passingMarks: number
  startTime: Date
  endTime: Date
  isActive: boolean
  allowedAttempts: number
  shuffleQuestions: boolean
  showResults: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Question {
  id: string
  examId: string
  type: 'MULTIPLE_CHOICE' | 'TRUE_FALSE' | 'SHORT_ANSWER' | 'ESSAY'
  question: string
  options?: string[] // For multiple choice
  correctAnswer: string
  marks: number
  order: number
  createdAt: Date
  updatedAt: Date
}

// Session and proctoring types
export interface ExamSession {
  id: string
  examId: string
  studentId: string
  startTime: Date
  endTime?: Date
  status: 'IN_PROGRESS' | 'COMPLETED' | 'TERMINATED'
  score?: number
  flagCount: number
  isProctored: boolean
  proctorId?: string
  createdAt: Date
  updatedAt: Date
}

export interface ProctoringFlag {
  id: string
  sessionId: string
  type: 'FACE_NOT_VISIBLE' | 'MULTIPLE_FACES' | 'SUSPICIOUS_ACTIVITY' | 'TAB_SWITCH' | 'COPY_PASTE'
  description: string
  timestamp: Date
  severity: 'LOW' | 'MEDIUM' | 'HIGH'
  resolved: boolean
  createdAt: Date
}
